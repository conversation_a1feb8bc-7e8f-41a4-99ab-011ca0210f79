package com.xinghuo.amazon.util;

import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Amazon工具类
 * 提供Amazon相关的工具方法
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
public class AmazonUtil {

    /**
     * ASIN正则表达式
     */
    private static final Pattern ASIN_PATTERN = Pattern.compile("/(dp|gp/product)/([A-Z0-9]{10})/");
    
    /**
     * 价格正则表达式
     */
    private static final Pattern PRICE_PATTERN = Pattern.compile("([0-9]+\\.?[0-9]*)");

    /**
     * 从URL中提取ASIN
     *
     * @param url Amazon商品URL
     * @return ASIN，如果提取失败返回null
     */
    public static String extractAsinFromUrl(String url) {
        if (StrXhUtil.isBlank(url)) {
            return null;
        }
        
        Matcher matcher = ASIN_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(2);
        }
        
        return null;
    }

    /**
     * 验证ASIN格式是否正确
     *
     * @param asin ASIN字符串
     * @return 是否为有效的ASIN
     */
    public static boolean isValidAsin(String asin) {
        if (StrXhUtil.isBlank(asin)) {
            return false;
        }
        
        // ASIN通常是10位字符，包含字母和数字
        return asin.matches("[A-Z0-9]{10}");
    }

    /**
     * 从字符串中提取价格
     *
     * @param priceStr 包含价格的字符串
     * @return 价格BigDecimal，如果提取失败返回null
     */
    public static BigDecimal extractPrice(String priceStr) {
        if (StrXhUtil.isBlank(priceStr)) {
            return null;
        }
        
        try {
            // 移除货币符号和其他非数字字符，保留数字和小数点
            String cleanStr = priceStr.replaceAll("[^0-9.]", "");
            
            if (StrXhUtil.isNotBlank(cleanStr)) {
                return new BigDecimal(cleanStr);
            }
        } catch (Exception e) {
            log.warn("提取价格失败: {}", priceStr, e);
        }
        
        return null;
    }

    /**
     * 从字符串中提取整数
     *
     * @param str 包含数字的字符串
     * @return 整数，如果提取失败返回null
     */
    public static Integer extractInteger(String str) {
        if (StrXhUtil.isBlank(str)) {
            return null;
        }

        try {
            // 移除非数字字符，但保留逗号用于分隔
            String cleanStr = str.replaceAll("[^0-9,]", "");
            // 移除逗号
            cleanStr = cleanStr.replaceAll(",", "");

            if (StrXhUtil.isNotBlank(cleanStr)) {
                // 防止数字过长导致Integer溢出
                if (cleanStr.length() > 9) {
                    log.warn("数字字符串过长，可能不是有效的评论数: {}", str);
                    return null;
                }
                return Integer.parseInt(cleanStr);
            }
        } catch (Exception e) {
            log.warn("提取整数失败: {}", str, e);
        }

        return null;
    }

    /**
     * 构建完整的Amazon URL
     *
     * @param path URL路径
     * @return 完整的Amazon URL
     */
    public static String buildFullUrl(String path) {
        if (StrXhUtil.isBlank(path)) {
            return null;
        }
        
        if (path.startsWith("http")) {
            return path;
        }
        
        if (path.startsWith("/")) {
            return "https://www.amazon.com" + path;
        }
        
        return "https://www.amazon.com/" + path;
    }

    /**
     * 清理商品标题
     *
     * @param title 原始标题
     * @return 清理后的标题
     */
    public static String cleanTitle(String title) {
        if (StrXhUtil.isBlank(title)) {
            return null;
        }
        
        // 移除多余的空白字符
        String cleanTitle = title.trim().replaceAll("\\s+", " ");
        
        // 限制标题长度
        if (cleanTitle.length() > 500) {
            cleanTitle = cleanTitle.substring(0, 500) + "...";
        }
        
        return cleanTitle;
    }

    /**
     * 验证Amazon URL是否有效
     *
     * @param url URL字符串
     * @return 是否为有效的Amazon URL
     */
    public static boolean isValidAmazonUrl(String url) {
        if (StrXhUtil.isBlank(url)) {
            return false;
        }
        
        return url.contains("amazon.com") || url.contains("amazon.cn") || 
               url.contains("amazon.co.uk") || url.contains("amazon.de") ||
               url.contains("amazon.fr") || url.contains("amazon.it") ||
               url.contains("amazon.es") || url.contains("amazon.ca") ||
               url.contains("amazon.com.au") || url.contains("amazon.co.jp");
    }

    /**
     * 获取任务状态描述
     *
     * @param status 状态码
     * @return 状态描述
     */
    public static String getStatusDescription(Integer status) {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case -2:
                return "404错误";
            case -1:
                return "无需处理";
            case 0:
                return "等待处理";
            case 1:
                return "处理中";
            case 2:
                return "处理完成";
            case 3:
                return "处理失败";
            case 8:
                return "ERP处理失败";
            case 9:
                return "被阻止";
            default:
                return "未知状态(" + status + ")";
        }
    }

    /**
     * 检查是否为赞助商品
     *
     * @param productInfo 商品信息
     * @return 是否为赞助商品
     */
    public static boolean isSponsored(String productInfo) {
        if (StrXhUtil.isBlank(productInfo)) {
            return false;
        }
        
        String lowerInfo = productInfo.toLowerCase();
        return lowerInfo.contains("sponsored") || lowerInfo.contains("广告") || 
               lowerInfo.contains("推广");
    }

    /**
     * 提取评分
     *
     * @param ratingStr 评分字符串
     * @return 评分BigDecimal
     */
    public static BigDecimal extractRating(String ratingStr) {
        if (StrXhUtil.isBlank(ratingStr)) {
            return null;
        }
        
        try {
            // 查找数字模式，如 "4.5 out of 5 stars"
            Pattern pattern = Pattern.compile("([0-9]+\\.?[0-9]*)");
            Matcher matcher = pattern.matcher(ratingStr);
            
            if (matcher.find()) {
                BigDecimal rating = new BigDecimal(matcher.group(1));
                // 确保评分在合理范围内
                if (rating.compareTo(BigDecimal.ZERO) >= 0 && rating.compareTo(new BigDecimal("5")) <= 0) {
                    return rating;
                }
            }
        } catch (Exception e) {
            log.warn("提取评分失败: {}", ratingStr, e);
        }
        
        return null;
    }


}
