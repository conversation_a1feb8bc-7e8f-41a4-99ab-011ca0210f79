<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.system.base.dao.PortalManageMapper">

    <sql id="selectPages">
        SELECT
        -- 门户管理表
        bpm.F_Id id,
        bpm.F_EnabledMark enabledMark,
        bpm.F_Platform platform,
        bpm.F_Description description,
        bpm.F_Home_Page_Mark homePageMark,
        bpm.F_TenantId tenantId,
        bpm.F_SortCode sortCode,
        bpm.F_System_Id systemId,
        bpm.F_Portal_Id portalId,
        bpm.F_CreatorTime creatorTime,
        bpm.F_LastModifyTime lastModifyTime,
        -- 门户表
        bp.F_Category categoryId,
        bp.F_FullName portalName,
        bd.F_FullName categoryName,
        -- 用户表
        us.F_RealName createUserName,
        us.F_Account createUserAccount,
        us2.F_RealName modifyUserName,
        us2.F_Account modifyUserAccount
        FROM
        base_portal_manage bpm
        LEFT JOIN
        base_portal bp
        ON
        bpm.F_Portal_Id = bp.F_Id
        LEFT JOIN
        base_user us
        ON
        bpm.F_CreatorUserId = us.F_Id
        LEFT JOIN
        base_user us2
        ON
        bpm.F_LastModifyUserId = us2.F_Id
        LEFT JOIN
        base_dictionarydata bd
        ON bp.F_Category = bd.F_Id
        WHERE 1 = 1
        <if test="pmPage.keyword != null and pmPage.keyword != ''">
            AND (bp.F_FullName LIKE #{pmPage.keyword} OR bpm.F_Description LIKE #{pmPage.keyword})
        </if>
        <if test="pmPage.category != null and pmPage.category != ''">
            AND
            bp.F_Category = #{pmPage.category}
        </if>
        <if test="pmPage.enabledMark != null and pmPage.enabledMark != ''">
            AND
            bpm.F_EnabledMark = #{pmPage.enabledMark}
            AND bp.F_EnabledMark = #{pmPage.enabledMark}
        </if>
        <if test="pmPage.platform != null and pmPage.platform != ''">
            AND
            bpm.F_Platform LIKE #{pmPage.platform}
        </if>
        <if test="pmPage.systemId != null and pmPage.systemId != ''">
            AND
            bpm.F_System_Id = #{pmPage.systemId}
        </if>
    </sql>

    <select id="selectPortalManageDoPage" resultType="com.xinghuo.system.base.model.portalmanage.PortalManagePageDO" parameterType="com.xinghuo.system.base.model.portalmanage.PortalManagePage">
        SELECT
        *
        FROM (<include refid="selectPages"></include>) TempTable
        ORDER BY
        sortCode ASC,
        creatorTime DESC
    </select>

    <select id="selectPortalManageDoList" resultType="com.xinghuo.system.base.model.portalmanage.PortalManagePageDO" parameterType="com.xinghuo.system.base.model.portalmanage.PortalManagePage">
        SELECT
        *
        FROM (<include refid="selectPages"></include>) TempTable
        ORDER BY
        sortCode ASC,
        creatorTime DESC
    </select>

</mapper>
