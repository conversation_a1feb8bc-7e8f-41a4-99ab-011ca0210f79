package com.xinghuo.amazon.util;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * Amazon变体数据
 */
@Data
public class AmazonVariationData {
    
    /**
     * 维度到ASIN的映射
     * 例如: {"0":"B0DLNVJM3R","1":"B0DLNSW59D","2":"B0DLNQLB5B","3":"B0DLNR3YSP"}
     */
    private Map<String, String> dimensionToAsinMap;
    
    /**
     * 变体值
     * 例如: {"color_name":["Blue Bead Spinner Kit","Light Pink Bead Spinner","Peach Pink Bead Spinner","Pink Bead Spinner Kit"]}
     */
    private Map<String, List<String>> variationValues;
    
    /**
     * 父体ASIN
     */
    private String parentAsin;
    
    /**
     * 当前ASIN
     */
    private String currentAsin;
    
    /**
     * 着陆ASIN
     */
    private String landingAsin;
}
