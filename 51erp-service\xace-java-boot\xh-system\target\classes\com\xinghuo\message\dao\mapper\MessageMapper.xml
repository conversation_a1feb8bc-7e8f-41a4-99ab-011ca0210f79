<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.message.dao.MessageMapper">

    <resultMap id="Message" type="com.xinghuo.message.entity.MessageEntity">
        <id column="F_Id" property="id"/>
        <result column="F_DefaultTitle" property="defaultTitle"/>
        <result column="F_Title" property="title"/>
        <result column="F_Type" property="type"/>
        <result column="F_FlowType" property="flowType"/>
        <result column="F_IsRead" property="isRead"/>
        <result column="F_CreatorTime" property="creatorTime"/>
        <result column="F_CreatorUserId" property="creatorUser"/>
        <result column="F_LastModifyTime" property="lastModifyTime"/>
        <result column="F_EnabledMark" property="enabledMark"/>
        <result column="F_LastModifyUserId" property="lastModifyUserId"/>
    </resultMap>

    <select id="getMessageList" parameterType="map" resultMap="Message">
        SELECT m.F_Id, m.F_Title, m.F_Type, r.F_IsRead, m.F_CreatorTime, m.F_CreatorUserId, m.F_LastModifyTime,
        m.F_LastModifyUserId,
        u.F_RealName,u.F_Account,m.F_FlowType,m.F_DefaultTitle FROM base_message m LEFT JOIN base_messagereceive r ON
        r.F_MessageId = m.F_Id
        LEFT JOIN base_user u ON u.F_Id = m.F_CreatorUserId where 1 = 1
        <if test="map.userId != null">
            AND r.F_UserId= #{map.userId}
        </if>
        <if test="map.keyword != null">
            AND (m.F_Title like #{map.keyword} OR m.F_DefaultTitle like #{map.keyword} OR u.F_RealName LIKE
            #{map.keyword} OR u.F_Account LIKE #{map.keyword})
        </if>
        <if test="map.type != null">
            AND m.F_Type = #{map.type}
        </if>
        <if test="map.isRead != null">
            AND r.F_IsRead = #{map.isRead}
        </if>
        ORDER BY F_LastModifyTime desc
    </select>

    <select id="getUnreadNoticeCount" parameterType="string" resultType="int">
        SELECT COUNT(1)  FROM base_messagereceive r LEFT JOIN base_message m ON m.F_Id = r.F_MessageId
        WHERE 1 = 1 AND r.F_UserId = #{userId} AND r.F_IsRead = 0 AND m.F_Type = 1
    </select>

    <select id="getUnreadCount" resultType="int">
        SELECT COUNT(1)  FROM base_messagereceive r LEFT JOIN base_message m ON m.F_Id = r.F_MessageId
        WHERE 1 = 1 AND r.F_UserId = #{userId} AND r.F_IsRead = 0 AND m.F_Type = #{type}
    </select>

    <select id="getUnreadMessageCount" parameterType="string" resultType="int">
        SELECT COUNT(1)  FROM base_messagereceive r LEFT JOIN base_message m ON m.F_Id = r.F_MessageId
        WHERE 1 = 1 AND r.F_UserId = #{userId} AND r.F_IsRead = 0 AND m.F_Type = 2
    </select>

    <select id="getUnreadSystemMessageCount" parameterType="string" resultType="int">
        SELECT COUNT(1)  FROM base_messagereceive r LEFT JOIN base_message m ON m.F_Id = r.F_MessageId
        WHERE 1 = 1 AND r.F_UserId = #{userId} AND r.F_IsRead = 0 AND m.F_Type = 3
    </select>

    <select id="getInfoDefault" parameterType="int" resultMap="Message">
        SELECT * FROM base_message WHERE 1 = 1 AND F_TYPE = #{type} ORDER BY F_CREATORTIME DESC
    </select>

</mapper>
