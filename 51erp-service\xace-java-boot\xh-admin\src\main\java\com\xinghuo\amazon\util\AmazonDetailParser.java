package com.xinghuo.amazon.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Amazon product detail page parser
 * Supports both single variant and multi-variant products
 */
@Slf4j
public class AmazonDetailParser {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Parse Amazon product detail page HTML
     */
    public static AmazonProductParseResult parseProductDetail(String htmlContent) {
        if (StringUtils.isBlank(htmlContent)) {
            log.warn("HTML content is empty");
            return null;
        }

        try {
            Document doc = Jsoup.parse(htmlContent);
            log.info("HTML parsing successful");

            // Extract SPU data
            AmazonSpuData spuData = extractSpuData(doc);
            if (spuData == null) {
                log.error("SPU data extraction failed");
                return null;
            }

            // Extract variation data
            AmazonVariationData variationData = extractVariationDataFromJs(doc);

            // Extract SKU data
            List<AmazonSkuData> skuDataList;
            if (variationData != null && variationData.getDimensionToAsinMap() != null 
                && !variationData.getDimensionToAsinMap().isEmpty()) {
                log.info("Multi-variant product detected, variant count: {}", variationData.getDimensionToAsinMap().size());
                skuDataList = extractSkuDataFromVariations(variationData, spuData.getAsin());
            } else {
                log.info("Single variant product detected");
                skuDataList = createSingleSkuFromPage(doc, spuData.getAsin());
            }

            AmazonProductParseResult result = new AmazonProductParseResult();
            result.setSpuData(spuData);
            result.setSkuDataList(skuDataList);
            result.setIsMultiVariant(skuDataList.size() > 1);

            log.info("Product parsing completed: ASIN={}, Title={}, SKU count={}, Type={}", 
                    spuData.getAsin(), 
                    spuData.getTitle() != null ? spuData.getTitle().substring(0, Math.min(50, spuData.getTitle().length())) + "..." : "Unknown",
                    skuDataList.size(),
                    result.getIsMultiVariant() ? "Multi-variant" : "Single variant");

            return result;

        } catch (Exception e) {
            log.error("Amazon detail page parsing failed", e);
            return null;
        }
    }

    /**
     * Extract SPU data
     */
    private static AmazonSpuData extractSpuData(Document doc) {
        log.info("Extracting SPU data...");
        
        AmazonSpuData spuData = new AmazonSpuData();

        try {
            // Extract ASIN (prefer parentAsin)
            String parentAsin = extractParentAsinFromJs(doc);
            if (StringUtils.isNotBlank(parentAsin)) {
                spuData.setAsin(parentAsin);
                log.info("Using parentAsin as SPU key: {}", parentAsin);
            } else {
                Element asinElement = doc.selectFirst("[data-asin]");
                if (asinElement != null) {
                    spuData.setAsin(asinElement.attr("data-asin"));
                } else {
                    spuData.setAsin("UNKNOWN_ASIN");
                }
                log.info("Using current page ASIN as SPU key: {}", spuData.getAsin());
            }

            // Extract title
            Element titleElement = doc.selectFirst("#productTitle");
            if (titleElement != null) {
                spuData.setTitle(titleElement.text().trim());
            }

            // Extract brand
            Element brandElement = doc.selectFirst("#bylineInfo");
            if (brandElement != null) {
                String brandText = brandElement.text().trim();
                Pattern brandPattern = Pattern.compile("Visit the (.+?) Store");
                Matcher brandMatcher = brandPattern.matcher(brandText);
                if (brandMatcher.find()) {
                    spuData.setBrand(brandMatcher.group(1));
                } else {
                    spuData.setBrand(brandText);
                }
            }

            // Extract rating
            Element ratingElement = doc.selectFirst(".a-icon-star-mini .a-icon-alt");
            if (ratingElement != null) {
                String ratingText = ratingElement.text().trim();
                BigDecimal rating = AmazonUtil.extractRating(ratingText);
                spuData.setRating(rating);
            }

            // Extract review count
            Element reviewElement = doc.selectFirst("#acrCustomerReviewText");
            if (reviewElement != null) {
                String reviewText = reviewElement.text().trim();
                Integer reviewCount = AmazonUtil.extractInteger(reviewText);
                spuData.setReviewCount(reviewCount);
            }

            // Extract main image URL
            Element mainImageElement = doc.selectFirst("#landingImage, #imgBlkFront");
            if (mainImageElement != null) {
                String imageUrl = mainImageElement.attr("src");
                if (StringUtils.isBlank(imageUrl)) {
                    imageUrl = mainImageElement.attr("data-src");
                }
                spuData.setMainImageUrl(imageUrl);
            }

            // Extract all image URLs
            List<String> imageUrls = new ArrayList<>();
            Elements imageElements = doc.select("#altImages img, .a-carousel img");
            for (Element img : imageElements) {
                String imgUrl = img.attr("src");
                if (StringUtils.isBlank(imgUrl)) {
                    imgUrl = img.attr("data-src");
                }
                if (StringUtils.isNotBlank(imgUrl) && imgUrl.contains("amazon.com")) {
                    imageUrls.add(imgUrl);
                }
            }
            spuData.setImageUrls(imageUrls.subList(0, Math.min(10, imageUrls.size())));

            // Extract bullet points
            List<String> bulletPoints = new ArrayList<>();
            Elements bulletElements = doc.select("#featurebullets_feature_div li span.a-list-item");
            for (Element bullet : bulletElements) {
                String text = bullet.text().trim();
                if (StringUtils.isNotBlank(text) && text.length() > 10) {
                    bulletPoints.add(text);
                }
            }
            spuData.setBulletPoints(bulletPoints.subList(0, Math.min(5, bulletPoints.size())));

            // Extract description
            Element descElement = doc.selectFirst("#productDescription, #aplus");
            if (descElement != null) {
                String description = descElement.text().trim();
                spuData.setDescription(description.length() > 2000 ? description.substring(0, 2000) : description);
            }

            // Extract product details
            Map<String, String> productDetails = new HashMap<>();
            Map<String, String> productAttributes = new HashMap<>();

            String[] detailSelectors = {
                "#prodDetails table.prodDetTable tr",
                "#productDetails_detailBullets_sections1 tr",
                "#productDetails_expanderTables tr",
                "#technicalSpecifications_section_1 tr"
            };

            for (String selector : detailSelectors) {
                Elements detailElements = doc.select(selector);
                for (Element row : detailElements) {
                    String key = null;
                    String value = null;

                    Elements cells = row.select("td");
                    if (cells.size() >= 2) {
                        key = cells.get(0).text().trim();
                        value = cells.get(1).text().trim();
                    } else {
                        Element thElement = row.selectFirst("th");
                        Element tdElement = row.selectFirst("td");
                        if (thElement != null && tdElement != null) {
                            key = thElement.text().trim();
                            value = tdElement.text().trim();
                        }
                    }

                    if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value) && !"Customer Reviews".equals(key)) {
                        key = key.replace(":", "").trim();
                        productDetails.put(key, value);
                        productAttributes.put(key, value);
                    }
                }
            }

            spuData.setProductDetails(productDetails);
            spuData.setProductAttributes(productAttributes);

            // Extract category path
            List<String> categoryPath = new ArrayList<>();
            Elements breadcrumbElements = doc.select("#wayfinding-breadcrumbs_feature_div a");
            for (Element breadcrumb : breadcrumbElements) {
                String text = breadcrumb.text().trim();
                if (StringUtils.isNotBlank(text) && !"Home".equals(text) && !"Amazon.com".equals(text)) {
                    categoryPath.add(text);
                }
            }
            spuData.setCategoryPath(String.join(" > ", categoryPath));

            log.info("SPU data extraction completed: {} - {}", spuData.getAsin(), 
                    spuData.getTitle() != null ? spuData.getTitle().substring(0, Math.min(50, spuData.getTitle().length())) + "..." : "Unknown title");
            return spuData;

        } catch (Exception e) {
            log.error("SPU data extraction failed", e);
            return null;
        }
    }

    /**
     * Extract parentAsin from JavaScript data
     */
    private static String extractParentAsinFromJs(Document doc) {
        Elements scriptTags = doc.select("script");
        for (Element script : scriptTags) {
            String scriptContent = script.html();
            if (StringUtils.isNotBlank(scriptContent) && scriptContent.contains("parentAsin")) {
                Pattern parentPattern = Pattern.compile("\"parentAsin\"\\s*:\\s*\"([^\"]+)\"");
                Matcher parentMatcher = parentPattern.matcher(scriptContent);
                if (parentMatcher.find()) {
                    return parentMatcher.group(1);
                }
            }
        }
        return null;
    }

    /**
     * Extract variation data from JavaScript
     */
    private static AmazonVariationData extractVariationDataFromJs(Document doc) {
        log.info("Extracting variation data from JavaScript...");

        Elements scriptTags = doc.select("script");
        AmazonVariationData variationData = new AmazonVariationData();

        for (Element script : scriptTags) {
            String scriptContent = script.html();
            if (StringUtils.isBlank(scriptContent) || !scriptContent.contains("dimensionToAsinMap")) {
                continue;
            }

            try {
                // Extract dimensionToAsinMap
                Pattern dimensionPattern = Pattern.compile("\"dimensionToAsinMap\"\\s*:\\s*(\\{[^}]+\\})");
                Matcher dimensionMatcher = dimensionPattern.matcher(scriptContent);
                if (dimensionMatcher.find()) {
                    String dimensionMapJson = dimensionMatcher.group(1);
                    Map<String, String> dimensionMap = objectMapper.readValue(dimensionMapJson, 
                            new TypeReference<Map<String, String>>() {});
                    variationData.setDimensionToAsinMap(dimensionMap);
                    log.info("Found dimensionToAsinMap: {}", dimensionMap);
                }

                // Extract variationValues
                Pattern variationPattern = Pattern.compile("\"variationValues\"\\s*:\\s*(\\{[^}]+\\})");
                Matcher variationMatcher = variationPattern.matcher(scriptContent);
                if (variationMatcher.find()) {
                    String variationValuesJson = variationMatcher.group(1);
                    JsonNode variationNode = objectMapper.readTree(variationValuesJson);
                    Map<String, List<String>> variationValues = new HashMap<>();
                    
                    variationNode.fields().forEachRemaining(entry -> {
                        String key = entry.getKey();
                        JsonNode valueNode = entry.getValue();
                        List<String> values = new ArrayList<>();
                        if (valueNode.isArray()) {
                            valueNode.forEach(node -> values.add(node.asText()));
                        }
                        variationValues.put(key, values);
                    });
                    
                    variationData.setVariationValues(variationValues);
                    log.info("Found variationValues: {}", variationValues);
                }

                // Extract parentAsin
                Pattern parentPattern = Pattern.compile("\"parentAsin\"\\s*:\\s*\"([^\"]+)\"");
                Matcher parentMatcher = parentPattern.matcher(scriptContent);
                if (parentMatcher.find()) {
                    variationData.setParentAsin(parentMatcher.group(1));
                    log.info("Found parentAsin: {}", parentMatcher.group(1));
                }

                break;

            } catch (Exception e) {
                log.warn("Variation data parsing failed: {}", e.getMessage());
            }
        }

        return variationData.getDimensionToAsinMap() != null ? variationData : null;
    }

    /**
     * Extract SKU data from variations
     */
    private static List<AmazonSkuData> extractSkuDataFromVariations(AmazonVariationData variationData, String spuAsin) {
        log.info("Extracting SKU data from variations...");

        List<AmazonSkuData> skuDataList = new ArrayList<>();

        if (variationData.getDimensionToAsinMap() == null || variationData.getDimensionToAsinMap().isEmpty()) {
            log.warn("No variation data found, creating default SKU");
            return skuDataList;
        }

        Map<String, String> dimensionMap = variationData.getDimensionToAsinMap();
        Map<String, List<String>> variationValues = variationData.getVariationValues();

        List<String> sizeNames = variationValues != null ? variationValues.get("size_name") : null;
        List<String> colorNames = variationValues != null ? variationValues.get("color_name") : null;

        for (Map.Entry<String, String> entry : dimensionMap.entrySet()) {
            String index = entry.getKey();
            String asin = entry.getValue();

            AmazonSkuData skuData = new AmazonSkuData();
            skuData.setAsin(asin);
            skuData.setParentAsin(spuAsin);
            skuData.setCurrency("USD");
            skuData.setStockStatus("In Stock");

            Map<String, String> variationAttributes = new HashMap<>();
            String displayName = "Variant " + index;

            try {
                if (index.contains("_")) {
                    String[] parts = index.split("_");
                    if (parts.length >= 2) {
                        int sizeIndex = Integer.parseInt(parts[0]);
                        int colorIndex = Integer.parseInt(parts[1]);

                        if (sizeNames != null && sizeIndex < sizeNames.size()) {
                            variationAttributes.put("Size", sizeNames.get(sizeIndex));
                        }

                        if (colorNames != null && colorIndex < colorNames.size()) {
                            variationAttributes.put("Color", colorNames.get(colorIndex));
                        }

                        displayName = String.format("%s - %s",
                                variationAttributes.getOrDefault("Color", "Unknown"),
                                variationAttributes.getOrDefault("Size", "Unknown"));
                    }
                } else {
                    int colorIndex = Integer.parseInt(index);
                    if (colorNames != null && colorIndex < colorNames.size()) {
                        variationAttributes.put("Color", colorNames.get(colorIndex));
                        displayName = colorNames.get(colorIndex);
                    }
                }
            } catch (NumberFormatException e) {
                log.debug("Failed to parse variation index: {}", index);
            }

            skuData.setVariationAttributes(variationAttributes);
            log.info("SKU {}: {}", asin, displayName);
            skuDataList.add(skuData);
        }

        log.info("Extracted {} SKU variants", skuDataList.size());
        return skuDataList;
    }

    /**
     * Create single SKU data for single variant product
     */
    private static List<AmazonSkuData> createSingleSkuFromPage(Document doc, String parentAsin) {
        log.info("Creating single variant SKU data...");

        AmazonSkuData skuData = new AmazonSkuData();
        skuData.setAsin(parentAsin);
        skuData.setParentAsin(parentAsin);
        skuData.setCurrency("USD");
        skuData.setStockStatus("In Stock");
        skuData.setVariationAttributes(new HashMap<>());

        // Extract price
        Element priceElement = doc.selectFirst(".a-price .a-offscreen");
        if (priceElement != null) {
            String priceText = priceElement.text().trim();
            BigDecimal price = AmazonUtil.extractPrice(priceText);
            skuData.setPrice(price);
            log.info("Price: ${}", price);
        }

        // Extract main image
        Element mainImageElement = doc.selectFirst("#landingImage, #imgBlkFront");
        if (mainImageElement != null) {
            String imageUrl = mainImageElement.attr("src");
            if (StringUtils.isBlank(imageUrl)) {
                imageUrl = mainImageElement.attr("data-src");
            }
            skuData.setImageUrl(imageUrl);
            log.info("Main image: {}", imageUrl != null ? imageUrl.substring(0, Math.min(50, imageUrl.length())) + "..." : "None");
        }

        // Extract stock status
        String[] stockSelectors = {
            "#availability span",
            "#availabilityInsideBuyBox_feature_div span",
            ".a-color-success",
            ".a-color-state",
            ".a-color-price"
        };

        boolean stockFound = false;
        for (String selector : stockSelectors) {
            Element availabilityElement = doc.selectFirst(selector);
            if (availabilityElement != null) {
                String availabilityText = availabilityElement.text().trim();
                if (StringUtils.isNotBlank(availabilityText)) {
                    if (availabilityText.contains("In Stock")) {
                        skuData.setStockStatus("In Stock");
                    } else if (availabilityText.contains("Out of Stock")) {
                        skuData.setStockStatus("Out of Stock");
                    } else if (availabilityText.toLowerCase().contains("only") && availabilityText.toLowerCase().contains("left")) {
                        Pattern quantityPattern = Pattern.compile("only\\s+(\\d+)\\s+left", Pattern.CASE_INSENSITIVE);
                        Matcher quantityMatcher = quantityPattern.matcher(availabilityText);
                        if (quantityMatcher.find()) {
                            String quantity = quantityMatcher.group(1);
                            skuData.setStockStatus("Only " + quantity + " left in stock");
                            skuData.setStockQuantity(Integer.parseInt(quantity));
                        } else {
                            skuData.setStockStatus(availabilityText.length() > 50 ? availabilityText.substring(0, 50) : availabilityText);
                        }
                    } else {
                        skuData.setStockStatus(availabilityText.length() > 50 ? availabilityText.substring(0, 50) : availabilityText);
                    }

                    log.info("Stock status: {}", skuData.getStockStatus());
                    stockFound = true;
                    break;
                }
            }
        }

        if (!stockFound) {
            skuData.setStockStatus("Unknown");
            log.info("Stock information not found, set to Unknown");
        }

        log.info("Single variant SKU creation completed");
        return Arrays.asList(skuData);
    }
}
