package com.xinghuo.amazon.util;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Amazon SPU（父体）数据
 */
@Data
public class AmazonSpuData {
    
    /**
     * ASIN（Amazon Standard Identification Number）
     */
    private String asin;
    
    /**
     * 商品标题
     */
    private String title;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 评分
     */
    private BigDecimal rating;
    
    /**
     * 评价数量
     */
    private Integer reviewCount;
    
    /**
     * 主图URL
     */
    private String mainImageUrl;
    
    /**
     * 所有图片URLs
     */
    private List<String> imageUrls;
    
    /**
     * 五点描述
     */
    private List<String> bulletPoints;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 产品详情
     */
    private Map<String, String> productDetails;
    
    /**
     * 产品参数属性
     */
    private Map<String, String> productAttributes;
    
    /**
     * 类目路径
     */
    private String categoryPath;
}
